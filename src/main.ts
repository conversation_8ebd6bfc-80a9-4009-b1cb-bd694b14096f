// 启动链路追踪
import { Tracer } from '@yqz/nest';
Tracer.start();
require("module-alias/register"); /* tslint:disable-line */
require('source-map-support').install();

import * as _ from "lodash";
console.log('===================================')
_.keys(process.env).forEach(key => {
  if (key.indexOf('YQZ_') >= 0 || key.indexOf('TYPEORM_') >= 0) {
    console.log(`${key}: ${process.env[key]}`);
  }
})
console.log('===================================')

import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { NestFactory } from "@nestjs/core";
import { AppModule } from "./app.module";
import { ValidationPipe } from "@nestjs/common";
import { MyLogger } from "./modules/logger/my-logger.service";
import { getEnvMiddleware, LoggingInterceptor, paseUserMiddleware } from "@yqz/nest";
import * as fs from "fs";
import { AllExceptionsFilter } from "@yqz/nest";
import { TransformInterceptor } from "@yqz/nest";
import { ReportModule } from "./modules/report/report.module";

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    logger: new MyLogger()
  });

  app.useGlobalPipes(new ValidationPipe());
  app.useGlobalInterceptors(new LoggingInterceptor());
  app.useGlobalInterceptors(new TransformInterceptor())
  app.useGlobalFilters(new AllExceptionsFilter())
  app.use(getEnvMiddleware)
  app.use(paseUserMiddleware)


  // https://docs.nestjs.com/openapi/other-features#multiple-specifications
  const config = new DocumentBuilder()
    .setTitle(process.env.YQZ_SERVICE_NAME)
    .setDescription(`${process.env.YQZ_SERVICE_NAME} api spec`)
    .setVersion('1.0')
    .addServer('https://cs.1qizhuang.com/api/reports')
    .addServer(`http://localhost:${process.env.YQZ_PORT}`)
    .addBearerAuth()
    .build();
  const document = SwaggerModule.createDocument(app, config, { include: [ReportModule] });
  fs.writeFileSync('swagger/default.json', JSON.stringify(document))
  SwaggerModule.setup('docs', app, document, { swaggerOptions: { persistAuthorization: true } });


  // await app.startAllMicroservicesAsync();
  await app.listen(process.env.YQZ_PORT);
  console.log(`listening on port ${process.env.YQZ_TARGET_ENV}:${process.env.YQZ_PORT}`);
}

//这个钩子用于优雅关闭 OpenTelemetry SDK，确保应用关闭时 trace 数据能完整上报，不会丢失。
process.on('SIGTERM', () => {
  Tracer.shutdown()
    .then(() => console.log('Tracing terminated'))
    .catch((error) => console.log('Error terminating tracing', error))
    .finally(() => process.exit(0));
});

bootstrap();
