import { Injectable } from "@nestjs/common";
import * as _ from "lodash";
import { DateReport } from "../../../dto/data-report.dto";
import { AiDetectionDataReport } from "../dto/ai-detection-data-report.dto";
import { DateReportInterface } from "../dto/data-report.interface.dto";
import { Chart } from "../../../chart/chart.type";
import { ProjectDetectionDao } from "@src/modules/report/domain/camera/project-detection/dao/detection.dao";
import { DateReportModule } from "../../../dto/data-report-module.dto";
import { ProjectService } from "@src/modules/report/domain/bgw/project/service/project.service";
import { YqzMedia } from "@src/modules/report/domain/bgw/media/type/media.type";
import { Detection } from "@src/modules/report/domain/camera/project-detection/dto/detection.dto";
import { YqzMediaType } from "@yqz/nest/lib/types/media.type";
import { ChartFactory } from "../../../chart/chart.factory";
import { CheckFormatCsv, DevTimeout, <PERSON><PERSON><PERSON><PERSON>, YqzException } from "@yqz/nest";
import * as DateFns from "date-fns";
import { CustomChart } from "../../../../custom-charts/abstract/custom-chart.abstract";
import { SqlFactory } from "../../../../sql-generate/factory/sql-factory";
import { Sql } from "../../../../sql-generate/type/sql.type";
import { DepartmentDao } from "@src/modules/report/domain/bgw/department/dao/department.dao";
import { CustomChartType } from "../../../../custom-charts/type/custom-chart.type";
import { CompanyDecorationDao } from "@src/modules/report/domain/bgw/common/company-decoration.dao";
import { ChartDateUtil } from "@src/util/chart-date.util";
import { CompanyService } from "@src/modules/report/domain/bgw/company/service/company.service";
import { DataReportConfigService } from "../../data-report-config.service";
import { DataReportModuleFilter } from "../../../dto/data-report-module-filter.dto";
import { Company } from "@src/modules/report/domain/bgw/company/dto/company.dto";

/**
 * @summary 数据看板 - ai监理
 * <AUTHOR>
 * @class AiDetectionDataReportService
 */
@Injectable()
export class AiDetectionDataReportService extends CustomChart {
    public readonly logger = new MyLogger(AiDetectionDataReportService.name);

    constructor(
        readonly dataReportConfigService: DataReportConfigService,
        readonly sqlFactory: SqlFactory,
        readonly departmentDao: DepartmentDao,
        private readonly projectDetectionDao: ProjectDetectionDao,
        private readonly projectService: ProjectService,
        private readonly chartFactory: ChartFactory,
        private readonly companyDecorationDao: CompanyDecorationDao,
        private readonly companyService: CompanyService
    ) {
        super({
            dataReportConfigService,
            sqlFactory,
            departmentDao
        });
    }

    /**
     * 获取报告图表数据
     * @param params 
     * @returns 
     */
    async getChartData(params: AiDetectionDataReport.ModuleReq): Promise<DateReportModule.DistributeDataModuleRes> {
        this.logger.log(`getChartData params: ${JSON.stringify(params)}`);
        const { dimension } = params;
        //处理图表数据参数
        params = await this.handelGetChartDataParams(params);
        //获取数据点id列表
        const { idList } = await this.getTopicIdList(params) as { idList: string[] };
        //获取图表数据方法
        const method = this.getDimensionChartDataFunction(dimension);
        if (!method) throw new YqzException(`维度类型不支持`);
        //获取图表数据
        const result = await method({ ...params, idList });
        return result;
    }

    /**
     * 数据点筛选项
     * @returns 
     */
    async getSubjectOption(companyId: string, subject: DateReport.Subject, businessCategory: 'home_decor' | 'construction'): Promise<DateReportInterface.SubjectOptionRes[]> {
        const result = await super.getSubjectOption(companyId, subject, businessCategory);
        if (_.isEmpty(result)) return result;
        const topics = result[0].subject.topic;
        for (const topic of topics) {
            //分布和趋势的name 要加上 topicName
            topic.dimension = topic.dimension.map((item) => {
                if (item.value === AiDetectionDataReport.Dimension.Group || item.value === AiDetectionDataReport.Dimension.GroupTrend) {
                    return { ...item, name: `${AiDetectionDataReport.TopicName[topic.value]}${item.name}` };
                } else return item;
            });
        }
        return result;
    }

    /**
     * 获取图表必要筛选条件(处理topic筛选和sql筛选的映射关系)
     * @param params 
     */
    async getChartSqlFilters(params: DateReportInterface.GetTopicIdListReq): Promise<{ companyId: string, subject: DateReport.Subject, topic: string, addFilters: Sql.Column[] }> {
        const { companyId, topic, from, to, departmentId } = params;
        const timeFrame = ChartDateUtil.getTimeFrame({ from, to });
        //查询报表的部门数据类型(根据用户所登陆的公司)
        const entity = await this.companyDecorationDao.search(companyId);
        const deptDataType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || 'real_time';
        //根据部门id列表查询部门数据切片的工地id列表
        const etlDateList = await this.getDeptEtlDateList({ departmentId, deptDataType, startTime: timeFrame.startTime, endTime: timeFrame.endTime, etlDataKey: [CustomChartType.EtlDataKey.ProjectIdList] });
        const projectIds = etlDateList?.projectIdList || [];
        const subject = DateReport.Subject.AiDetection;
        const addFilters: Sql.Column[] = [
            { key: "projectId", value: projectIds, operator: Sql.Operator.in },
            { key: "detectionTime", value: timeFrame.startTime, operator: Sql.Operator.greater_than_or_equal },
            { key: "detectionTime", value: timeFrame.endTime, operator: Sql.Operator.less_than }
        ];
        this.logger.log(`getChartSqlFilters params: ${JSON.stringify(params)}, addFilters: ${JSON.stringify(addFilters)}`);
        return { companyId, subject, topic, addFilters };
    }

    /**
     * 获取数据分析筛选项
     * @param params 
     * @returns 
     */
    getFilterMap(): { [key in string]: { filterName: string, filterField: string, filterType: DataReportModuleFilter.FilterControlType } } {
        return AiDetectionDataReport.FilterMap;
    }

    /**
     * 数据分析筛选项
     * @param topic 
     */
    async getAnalysisOption(params: AiDetectionDataReport.AnalysisOptionReq): Promise<AiDetectionDataReport.AnalysisOptionRes> {
        const { companyId, topic, from, to, dataType, departmentId, businessCategory, companyType } = params;
        if (!companyId || !topic) return null;
        const result: AiDetectionDataReport.AnalysisOptionRes = { projectManager: [], reportType: [], projectAddress: "" };
        const linkCompany: DataReportModuleFilter.SelectFilterValue[] = [];
        const deviceDepartment: DataReportModuleFilter.SelectFilterValue[] = [];
        //获取数据点id列表
        const { idList } = await this.getTopicIdList({ companyId, topic, from, to, dataType, departmentId }) as { idList: string[] };
        const promiseList = [];
        //所属公司
        promiseList.push(companyType == Company.CompanyType.Conglomerate ? topic === AiDetectionDataReport.Topic.TidinessDetection ? this.projectDetectionDao.getTidinessGroupCompany({ idList }) : this.projectDetectionDao.getSecurityGroupCompany({ idList }) : []);//所属公司
        //工地负责人
        promiseList.push(topic === AiDetectionDataReport.Topic.TidinessDetection ? this.projectDetectionDao.getTidinessGroupProjectManager({ idList }) : this.projectDetectionDao.getSecurityGroupProjectManager({ idList }));//工地负责人
        //服务厂商
        promiseList.push(
            businessCategory === 'construction' ?
                topic === AiDetectionDataReport.Topic.TidinessDetection ?
                    this.projectDetectionDao.countTidinessProjectGroupDeviceDepartment({ idList }) :
                    this.projectDetectionDao.countSecurityProjectGroupDeviceDepartment({ idList, detectionType: topic === AiDetectionDataReport.Topic.ActionDetection ? "action" : "body" })
                : []
        );
        const [linkCompanyRes, projectManagerRes, deviceDepartmentRes] = await Promise.all(promiseList);
        (linkCompanyRes || []).filter(res => !!res.companyId).map(res => linkCompany.push({ value: res.companyId, name: res.companyName }));
        (projectManagerRes || []).filter(res => !!res.projectManagerId).map(res => result.projectManager.push({ value: res.projectManagerId, name: res.projectManagerName }));
        (deviceDepartmentRes || []).filter(res => !!res.deviceDepartmentId).map(res => deviceDepartment.push({ value: res.deviceDepartmentId, name: res.deviceDepartmentName }));
        //报告类型
        switch (topic) {
            case AiDetectionDataReport.Topic.TidinessDetection://整洁度
                result.reportType.push(...AiDetectionDataReport.TidinessReportType.getOption());
                break;
            case AiDetectionDataReport.Topic.ActionDetection://行为识别
                result.reportType.push(...AiDetectionDataReport.ActionReportType.getOption());
                break;
            case AiDetectionDataReport.Topic.BodyDetection://物体识别
                result.reportType.push(...AiDetectionDataReport.BodyReportType.getOption());
                break;
        }
        //集团公司 独有筛选项
        if (companyType === Company.CompanyType.Conglomerate) Object.assign(result, { linkCompany });
        //营建公司 独有筛选项
        if (businessCategory === 'construction') Object.assign(result, { deviceDepartment });
        return result
    }

    /**
     * 数据分析列表
     * @param params 
     */
    @CheckFormatCsv(AiDetectionDataReport.AnalysisListCsv)
    async getAnalysisList(params: AiDetectionDataReport.AnalysisListReq): Promise<{ total: number; items: Detection.DetectionDetail[]; }> {
        const { companyType, companyId, topic, from, to, dataType, departmentId, businessCategory } = params;
        const result: { total: number; items: Detection.DetectionDetail[]; } = { total: 0, items: [] };
        if (!companyId || !topic) return result;
        //获取数据点id列表
        const { idList } = await this.getTopicIdList({ companyId, topic, from, to, dataType, departmentId }) as { idList: string[] };
        if (_.isEmpty(idList)) return result;
        params.idList = idList;
        //查询列表
        let detectionRes = null;
        switch (topic) {
            case AiDetectionDataReport.Topic.TidinessDetection://行为检测
                detectionRes = await this.projectDetectionDao.searchTidinessList(params);
                break;
            case AiDetectionDataReport.Topic.ActionDetection://行为检测
                detectionRes = await this.projectDetectionDao.searchSecurityList({ ...params, detectionType: 'action' })
                break;
            case AiDetectionDataReport.Topic.BodyDetection://物体检测
                detectionRes = await this.projectDetectionDao.searchSecurityList({ ...params, detectionType: 'body' })
                break;
        }
        //查询其他数据
        const projectIds = [];
        const companyIds = [];
        detectionRes.items.forEach(item => {
            projectIds.push(item.projectId);
            companyIds.push(item.linkCompanyId);
        });
        const promiseList = [];
        promiseList.push(this.projectService.searchProjectInfoMap({ projectIds, businessCategory: params.businessCategory }));//工地信息
        promiseList.push(companyType === '4' ? this.companyService.searchCompanyInfoMap({ companyIds }) : {});//公司信息
        const [projectInfoMap, companyInfoMap] = await Promise.all(promiseList);
        //数据组装
        result.total = detectionRes.total;
        result.items = detectionRes.items.map(item => {
            const reportType = [];
            switch (topic) {
                case AiDetectionDataReport.Topic.TidinessDetection:
                    reportType.push(AiDetectionDataReport.TidinessReportType.getNameByCode(item.tidinessType));
                    break;
                case AiDetectionDataReport.Topic.ActionDetection:
                    reportType.push(...(item?.securityType || "").split(",").map(code => { return AiDetectionDataReport.ActionReportType.getNameByCode(code) }).filter(res => res));
                    break;
                case AiDetectionDataReport.Topic.BodyDetection:
                    reportType.push(...(item?.securityType || "").split(",").map(code => { return AiDetectionDataReport.BodyReportType.getNameByCode(code) }).filter(res => res));
                    break;
            }
            const result = {
                ...item,
                projectAddress: projectInfoMap[item.projectId]?.projectAddress || "",
                projectManagerId: projectInfoMap[item.projectId]?.projectManagerId || "",
                projectManagerName: projectInfoMap[item.projectId]?.projectManagerName || "",
                departmentId: projectInfoMap[item.projectId]?.departmentId || "",
                departmentName: projectInfoMap[item.projectId]?.departmentName || "",
                reportType: reportType,
                photo: new YqzMedia({ id: item.eventId, type: YqzMediaType.IMAGE, cover: item.photoUrl, resourceUrl: item.photoUrl, creationDate: "", address: "" })
            }
            //集团公司 独有数据
            if (companyType === '4') {
                Object.assign(result, {
                    linkCompanyName: companyInfoMap[item.linkCompanyId]?.companyName || "",
                });
            }
            //营建公司 独有数据
            if (businessCategory === 'construction') {
                Object.assign(result, {
                    deviceDepartmentId: projectInfoMap[item.projectId]?.deviceDepartmentId || "",
                    deviceDepartmentName: projectInfoMap[item.projectId]?.deviceDepartmentName || "",
                });
            }
            return result;
        })
        return result;
    }

    /**===================================
                处理图表数据
    ===================================**/
    getDimensionChartDataFunction(dimension: AiDetectionDataReport.Dimension) {
        //维度图表 方法映射
        const dimensionChartDataFunction: Readonly<{ [K in AiDetectionDataReport.Dimension]: (params: AiDetectionDataReport.DimensionReq) => Promise<DateReportModule.DistributeDataModuleRes> }> = {
            [AiDetectionDataReport.Dimension.ProjectDepartmentGroup]: this.getProjectDepartmentGroupChartData.bind(this), // 工地部门
            [AiDetectionDataReport.Dimension.ServiceVendorGroup]: this.getDeviceDepartmentGroupChartData.bind(this), // 服务厂商
            [AiDetectionDataReport.Dimension.Number]: this.getNumberChartData.bind(this), // 数值
            [AiDetectionDataReport.Dimension.GroupTrend]: this.getGroupTrendChartData.bind(this), // 分布趋势
            [AiDetectionDataReport.Dimension.Group]: this.getGroupChartData.bind(this),//分布
        };
        return dimensionChartDataFunction[dimension];
    }

    /**
     * 查询周期数据
     * 用于: 分布趋势图表数据
     * @param params 
     * @returns 
     */
    private async getGroupTrendData(params: AiDetectionDataReport.AnalysisDistributeReq): Promise<{ category: string, key: string, name: string, value: number }[]> {
        const { companyId, topic, from, to, departmentId } = params;
        if (!companyId || !topic || !from || !to || !departmentId) return [];
        const { idList } = await this.getTopicIdList({ companyId, topic, from, to, dataType: "day", departmentId }) as { idList: string[] };
        params.idList = idList;
        let detectionData: { count: string, reportType: string }[];
        const categoryFrom = DateFns.format(new Date(from), "MM.dd");
        const categoryTo = DateFns.format(new Date(to), "MM.dd");
        const category = `${categoryFrom}-${categoryTo}`;
        switch (topic) {
            case AiDetectionDataReport.Topic.TidinessDetection://整洁度检测
                detectionData = await this.projectDetectionDao.countTidinessGroupType(params);
                return detectionData.map(res => { return { category, key: AiDetectionDataReport.TidinessReportType.getValueByCode(res.reportType), name: AiDetectionDataReport.TidinessReportType.getNameByCode(res.reportType), value: Number(res?.count || 0) } });
            case AiDetectionDataReport.Topic.ActionDetection://行为检测
                detectionData = await this.projectDetectionDao.countSecurityGroupType({ ...params, detectionType: 'action' });
                return detectionData.map(res => { return { category, key: AiDetectionDataReport.ActionReportType.getValueByCode(res.reportType), name: AiDetectionDataReport.ActionReportType.getNameByCode(res.reportType), value: Number(res?.count || 0) } });
            case AiDetectionDataReport.Topic.BodyDetection://物体检测
                detectionData = await this.projectDetectionDao.countSecurityGroupType({ ...params, detectionType: 'body' });
                return detectionData.map(res => { return { category, key: AiDetectionDataReport.BodyReportType.getValueByCode(res.reportType), name: AiDetectionDataReport.BodyReportType.getNameByCode(res.reportType), value: Number(res?.count || 0) } });
            default: return [];
        }
    }

    /**
     * 获取工地部门分布图表数据
     * 维度: 工地部门分布
     * 支持的图表结构: 玫瑰图|饼图|环形图
     * @param params 
     * @returns 
     */
    private async getProjectDepartmentGroupChartData(params: AiDetectionDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { topic, chartType, departmentId } = params;
        if (!topic || !chartType) throw new YqzException("必传参数不能为空");
        //查询数据
        let detectionData = [];
        switch (topic) {
            case AiDetectionDataReport.Topic.TidinessDetection://整洁度检测
                detectionData = await this.projectDetectionDao.countTidinessGroupDepartment(params);
                break;
            case AiDetectionDataReport.Topic.ActionDetection://行为检测
                detectionData = await this.projectDetectionDao.countSecurityGroupDepartment({ ...params, detectionType: 'action' });
                break;
            case AiDetectionDataReport.Topic.BodyDetection://物体检测
                detectionData = await this.projectDetectionDao.countSecurityGroupDepartment({ ...params, detectionType: 'body' });
        }
        //处理图表结构: 工地部门分布 & 玫瑰图|饼图|环形图|柱状图(分布)
        const entity = await this.companyDecorationDao.search(params.companyId);
        const deptDataType = JSON.parse(entity.deliverKanbanSet)?.deptDateType || CustomChartType.DeptDataType.RealTime;
        const data: { key: string, name: string, value: number }[] = await this.handelDepartmentGroup({ deptDataType, currentDeptId: departmentId, from: params.from, to: params.to, data: detectionData });
        const title = params?.title || AiDetectionDataReport.Topic[topic];
        const name = AiDetectionDataReport.DimensionName[AiDetectionDataReport.Dimension.ProjectDepartmentGroup];
        const actionType = params?.actionType || Chart.ActionType.None;
        const gridColumn = params?.gridColumn || '1';
        let chartData = null;
        switch (chartType) {
            case Chart.ChartType.Rose:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Rose>({ chartType, title, name, optionKey: 'departmentId', data, actionType });
                break;
            case Chart.ChartType.Pie:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Pie>({ chartType, title, name, optionKey: 'departmentId', data, actionType });
                break;
            case Chart.ChartType.Ring:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Ring>({ chartType, title, name, optionKey: 'departmentId', data, actionType });
                break;
            case Chart.ChartType.BarDistribute:
                chartData = await this.chartFactory.createChart<Chart.ChartType.BarDistribute>({ chartType, title, name, optionKey: 'departmentId', data, actionType });
                break;
        }
        if (!chartData) throw new YqzException(`${title}图表数据处理失败`);
        //返回数据
        return {
            subject: DateReport.Subject.AiDetection,
            dimension: AiDetectionDataReport.Dimension.ProjectDepartmentGroup,
            topic, title, chartType, data: chartData, gridColumn
        }
    }

    /**
     * 获取服务厂商分布图表数据
     * 维度: 服务厂商分布
     * 支持的图表结构: 玫瑰图|饼图|环形图
     * @param params 
     * @returns 
     */
    private async getDeviceDepartmentGroupChartData(params: AiDetectionDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { topic, chartType, idList } = params;
        if (!topic || !chartType) throw new YqzException("必传参数不能为空");
        //查询数据
        let detectionData: { count: string, deviceDepartmentId: string, deviceDepartmentName: string }[] = [];
        switch (topic) {
            case AiDetectionDataReport.Topic.TidinessDetection://整洁度检测
                detectionData = await this.projectDetectionDao.countTidinessProjectGroupDeviceDepartment(params);
                break;
            case AiDetectionDataReport.Topic.ActionDetection://行为检测
                detectionData = await this.projectDetectionDao.countSecurityProjectGroupDeviceDepartment({ ...params, detectionType: 'action' });
                break;
            case AiDetectionDataReport.Topic.BodyDetection://物体检测
                detectionData = await this.projectDetectionDao.countSecurityProjectGroupDeviceDepartment({ ...params, detectionType: 'body' });
        }
        //处理图表结构: 服务厂商分布 & 玫瑰图|饼图|环形图|柱状图(分布)
        const data: { key: string, name: string, value: number }[] = [];
        detectionData.map(item => { data.push({ key: item.deviceDepartmentId || "", name: item.deviceDepartmentName || "无服务厂商", value: Number(item?.count || 0) }); });
        const title = params?.title || AiDetectionDataReport.TopicName[topic];
        const name = AiDetectionDataReport.DimensionName[AiDetectionDataReport.Dimension.ServiceVendorGroup];
        const actionType = params?.actionType || Chart.ActionType.None;
        const gridColumn = params?.gridColumn || '1';
        let chartData = null;
        switch (chartType) {
            case Chart.ChartType.Rose:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Rose>({ chartType, title, name, optionKey: 'deviceDepartmentId', data, actionType });
                break;
            case Chart.ChartType.Pie:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Pie>({ chartType, title, name, optionKey: 'deviceDepartmentId', data, actionType });
                break;
            case Chart.ChartType.Ring:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Ring>({ chartType, title, name, optionKey: 'deviceDepartmentId', data, actionType });
                break;
            case Chart.ChartType.BarDistribute:
                chartData = await this.chartFactory.createChart<Chart.ChartType.BarDistribute>({ chartType, title, name, optionKey: 'deviceDepartmentId', data, actionType });
                break;
        }
        if (!chartData) throw new YqzException(`${title}图表数据处理失败`);
        //返回数据
        return {
            subject: DateReport.Subject.AiDetection,
            dimension: AiDetectionDataReport.Dimension.ServiceVendorGroup,
            topic, title, chartType, data: chartData, gridColumn
        }
    }

    /**
     * 获取数值图表数据
     * 维度: 数值
     * 支持的图表结构: 数值
     * @param params 
     */
    private async getNumberChartData(params: AiDetectionDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { topic, chartType } = params;
        if (!topic || !chartType) throw new YqzException("必传参数不能为空");
        //查询数据
        let detectionData = { count: '0' };
        switch (topic) {
            case AiDetectionDataReport.Topic.TidinessDetection://整洁度检测
                detectionData = await this.projectDetectionDao.countTidiness(params);
                break;
            case AiDetectionDataReport.Topic.ActionDetection://行为检测
                detectionData = await this.projectDetectionDao.countSecurity({ ...params, detectionType: 'action' });
                break;
            case AiDetectionDataReport.Topic.BodyDetection://物体检测
                detectionData = await this.projectDetectionDao.countSecurity({ ...params, detectionType: 'body' });
        }
        //处理图表结构: 总数统计 & 数值
        const title = params?.title || AiDetectionDataReport.Topic[topic];
        const name = AiDetectionDataReport.DimensionName[AiDetectionDataReport.Dimension.Number];
        const actionType = params?.actionType || Chart.ActionType.None;
        const gridColumn = params?.gridColumn || '1';
        const chartData = await this.chartFactory.createChart<Chart.ChartType.Total>({ chartType: Chart.ChartType.Total, title, name, data: Number(detectionData?.count || 0), actionType });
        return {
            subject: DateReport.Subject.AiDetection,
            dimension: AiDetectionDataReport.Dimension.Number,
            topic, title, chartType, data: chartData, gridColumn
        }
    }

    /**
     * 获取报告分布趋势图表数据
     * 维度: 分布趋势
     * 支持的图表结构: 多维折线图
     * @param params 
     */
    private async getGroupTrendChartData(params: AiDetectionDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { companyId, topic, from, to, dataType, chartType } = params;
        if (!companyId || !topic || !from || !to || !dataType || !chartType) throw new YqzException("必传参数不能为空");
        const timeFrameList = ChartDateUtil.getTimeFrameList({ from, to, dataType });
        const promiseList = [];
        //查询周期数据
        for (const timeFrame of timeFrameList) promiseList.push(this.getGroupTrendData({ ...params, from: timeFrame.startTime, to: timeFrame.endTime }));
        const promiseResult: { category: string, key: string, name: string, value: number }[][] = await Promise.all(promiseList);
        const category = [];//x轴数据
        const dataMap = new Map<string, { key: string; name: string; value: number[] }>();//分组数据
        promiseResult.forEach(res => {
            category.push(res[0]?.category || "");
            res.forEach(({ key, name, value }) => {
                if (!dataMap.has(key)) dataMap.set(key, { key, name, value: [] });// 初始化分组
                dataMap.get(key)!.value.push(value);// 添加值
            });
        });
        //过滤掉7天数据都是0的结果
        const data = Array.from(dataMap.values()).filter(item => item.value.some(v => v !== 0));
        //处理图表结构: 工地分布趋势 & 多维折线图
        const title = params?.title || `${AiDetectionDataReport.TopicName[topic]}`;
        const name = `${AiDetectionDataReport.TopicName[topic]}${AiDetectionDataReport.DimensionName[AiDetectionDataReport.Dimension.GroupTrend]}`;
        const actionType = params?.actionType || Chart.ActionType.None;
        const gridColumn = params?.gridColumn || '1';
        const chartData = await this.chartFactory.createChart<Chart.ChartType.MultiLine>({ chartType: Chart.ChartType.MultiLine, title, name, category, data, actionType });
        return {
            subject: DateReport.Subject.AiDetection,
            dimension: AiDetectionDataReport.Dimension.GroupTrend,
            topic, title, chartType, data: chartData, gridColumn
        }
    }

    /**
     * 获取报告分布图表数据
     * 维度: 分布
     * 支持的图表结构: 玫瑰图|饼图|环形图
     * @param params 
     */
    private async getGroupChartData(params: AiDetectionDataReport.DimensionReq): Promise<DateReportModule.DistributeDataModuleRes> {
        const { topic, chartType } = params;
        if (!topic || !chartType) throw new YqzException("必传参数不能为空");
        //查询数据
        let detectionData: { count: string, reportType: string }[] = [];
        switch (topic) {
            case AiDetectionDataReport.Topic.TidinessDetection://整洁度检测
                detectionData = await this.projectDetectionDao.countTidinessGroupType(params);
                break;
            case AiDetectionDataReport.Topic.ActionDetection://行为检测
                detectionData = await this.projectDetectionDao.countSecurityGroupType({ ...params, detectionType: 'action' });
                break;
            case AiDetectionDataReport.Topic.BodyDetection://物体检测
                detectionData = await this.projectDetectionDao.countSecurityGroupType({ ...params, detectionType: 'body' });
                break;
        }
        //处理图表结构: 报告分布 & 玫瑰图|饼图|环形图|柱状图(分布)
        const dataList: { key: string, name: string, value: number }[] = [];
        (detectionData || []).map(item => {
            if (topic === AiDetectionDataReport.Topic.TidinessDetection) dataList.push({ key: AiDetectionDataReport.TidinessReportType.getValueByCode(item.reportType), name: AiDetectionDataReport.TidinessReportType.getNameByCode(item.reportType), value: Number(item?.count || 0) });
            if (topic === AiDetectionDataReport.Topic.ActionDetection) dataList.push({ key: AiDetectionDataReport.ActionReportType.getValueByCode(item.reportType), name: AiDetectionDataReport.ActionReportType.getNameByCode(item.reportType), value: Number(item?.count || 0) });
            if (topic === AiDetectionDataReport.Topic.BodyDetection) dataList.push({ key: AiDetectionDataReport.BodyReportType.getValueByCode(item.reportType), name: AiDetectionDataReport.BodyReportType.getNameByCode(item.reportType), value: Number(item?.count || 0) });
        });
        //过滤掉数是0的结果
        const data = dataList.filter(item => item.value !== 0);
        const title = params?.title || `${AiDetectionDataReport.TopicName[topic]}`;
        const name = `${AiDetectionDataReport.TopicName[topic]}${AiDetectionDataReport.DimensionName[AiDetectionDataReport.Dimension.Group]}`;
        const actionType = params?.actionType || Chart.ActionType.None;
        const gridColumn = params?.gridColumn || '1';
        let chartData = null;
        switch (chartType) {
            case Chart.ChartType.Rose:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Rose>({ chartType, title, name, optionKey: 'reportType', data, actionType });
                break;
            case Chart.ChartType.Pie:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Pie>({ chartType, title, name, optionKey: 'reportType', data, actionType });
                break;
            case Chart.ChartType.Ring:
                chartData = await this.chartFactory.createChart<Chart.ChartType.Ring>({ chartType, title, name, optionKey: 'reportType', data, actionType });
                break;
            case Chart.ChartType.BarDistribute:
                chartData = await this.chartFactory.createChart<Chart.ChartType.BarDistribute>({ chartType, title, name, optionKey: 'reportType', data, actionType });
                break;
        }
        return {
            subject: DateReport.Subject.AiDetection,
            dimension: AiDetectionDataReport.Dimension.Group,
            topic, title, chartType, data: chartData, gridColumn
        }
    }

}