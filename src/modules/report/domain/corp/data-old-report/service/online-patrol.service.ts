import { Injectable } from "@nestjs/common";
import { <PERSON>FormatCsv, <PERSON>T<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, YqzException } from "@yqz/nest";
import { Analytics } from "../type/analytics-util.type";
import { AnalyticsService } from "./analytics.service";
import { ProjectPatrolBoardDao } from "../dao/project-patrol.dao";
import { CompanyCustomConfigService } from "../../../bgw/company/service/company-custom-config.service";
import { ObjUtil } from "@src/util/obj.util";
import * as _ from "lodash";
import { ProjectDao } from "../../../bgw/project/dao/project.dao";
import { OnsitePatrolBoardDao } from "../dao/onsite-patrol.dao";
import { DateReport } from "../../data-report/dto/data-report.dto";
import { ProjectService } from "../../../bgw/project/service/project.service";
import { CorpReportBasicsService } from "../../corp-report/service/basics/corp-report-basics.service";
import { CompanyDao } from "../../../bgw/company/dao/company.dao";
import { format } from "date-fns";
import { ManagerService } from "../../../bgw/manager/service/manager.service";
import { DimDeviceDao } from "../../../etl/dao/dim-device.dao";
import { DepartmentMemberDao } from "../../../bgw/department/dao/department-member.dao";
import { ManagerDao } from "../../../bgw/manager/dao/manager.dao";
import { Company } from "../../../bgw/company/dto/company.dto";

@Injectable()
export class OnlinePatrolService {
    private readonly logger = new MyLogger(OnlinePatrolService.name);
    constructor(
        private readonly analyticsService: AnalyticsService,
        private readonly projectPatrolDao: ProjectPatrolBoardDao,
        private readonly companyCustomConfigService: CompanyCustomConfigService,
        private readonly projectDao: ProjectDao,
        private readonly onsitePatrolDao: OnsitePatrolBoardDao,
        private readonly projectService: ProjectService,
        private readonly corpReportBasicsService: CorpReportBasicsService,
        private readonly companyDao: CompanyDao,
        private readonly managerService: ManagerService,
        private readonly dimDeviceDao: DimDeviceDao,
        private readonly departmentMemberDao: DepartmentMemberDao,
        private readonly managerDao: ManagerDao,
    ) { }

    // @DevTimeout(100)
    async test111() {
        const total = await this.analyticsPerson({
            companyId: "*********",
            departmentId: "12543",
            from: "2024-01-01",
            to: "2025-01-31",
            roleName: "0"
        })
        console.log(`total: ${JSON.stringify(total)}`)
    }


    /** 线上巡检分析 选项  */
    async analyticsOptions(
        body: Analytics.Req
    ): Promise<Analytics.analyticsOptionsRes> {
        try {
            //统一处理数据报表请求参数
            const query = await this.analyticsService.onlineBodyParse({...body, module: DateReport.Subject.OnlinePatrol});
            if (!query) return {
                positionList: [],
                personList: [],
                linkCompany: []
            }

            // /** 再次过滤 公司配置 */
            const { noManager, isAllRole, dealPostList, dealManagerList } = await this.analyticsService.onlinePathConfig(query,DateReport.Subject.OnlinePatrol);
            if (noManager) return {
                positionList: [],
                personList: [],
                linkCompany: []
            }

            const data: Analytics.analyticsOptionsRes = await this.projectPatrolDao.analyticsOptions(query);

            // 根据部门id获取集团公司
            const companyIds = await this.corpReportBasicsService.searchCompanyIdsByDeptId(query.departmentId);
            const companyList = await this.companyDao.searchCompanyList({ companyIds });
            data.linkCompany = companyList.map(item => {
                return {
                    name: item.companyName,
                    value: item.id,
                };
            })
            if (!isAllRole && dealPostList instanceof Array) {
                data.positionList = _.uniqBy(dealPostList.map(post => ({ roleId: post.roleId, roleName: post.name })), "roleName")
            }

            if (!isAllRole && dealManagerList instanceof Array) {
                /** 为了 dealManager 实际巡检的人数 */
                const persons: Analytics.AnalyticsPersonRes = await this.projectPatrolDao.analyticsPerson(query);
                if (!isAllRole && dealManagerList instanceof Array) {
                    let dealManager = dealManagerList.length
                    dealManagerList.forEach(manager => {
                        let i = 0;
                        for (; i < persons.analyticPersons.length; i++) {
                            const item = persons.analyticPersons[i];
                            if (item.managerId == manager.managerId) break;
                        }
                        /** 如果 真实巡检人员 中没有 应该的巡检人员 则为这些人填补0为数据 */
                        if (i == persons.analyticPersons.length) {
                            /** 去掉 离职人员 未签到的人员数量 并且不把此人加入到巡检人员列表 */
                            if (manager.deleteFlag != 'N') {
                                dealManager--;
                                return;
                            }
                        }

                        /** 筛选出 所有的 巡检员工 + 应该巡检的在职员工 */
                        let j = 0;
                        for (; j < data.personList.length; j++) {
                            const item = data.personList[j];
                            if (item.managerId == manager.managerId) break;
                        }
                        if (j == data.personList.length) data.personList.push({ managerId: manager.managerId, nickName: manager.nickName })
                    })
                }
            }

            return data;
        } catch (e) {
            this.logger.log(`projectPatrolService analyticsOptions 错误: ${String(e)}`);
            throw new YqzException(`projectPatrolService analyticsOptions 错误: ${String(e)}`)
        }
    }

    /** 线上巡检分析 员工分布 */
    @CheckFormatCsv(Analytics.csvPerson, "analyticPersons")
    async analyticsPerson(body: Analytics.Req): Promise<Analytics.AnalyticsPersonRes> {
        try {
            //统一处理数据报表请求参数
            const query = await this.analyticsService.onlineBodyParse({...body, module: DateReport.Subject.OnlinePatrol});
            if (!query) return { addressSelects: [], analyticPersons: [] }
            /** 再次过滤 公司配置 并得到 本地需要分析的所有人员 dealManagerList: {}[] | null */
            let { noManager, isAllRole, dealManagerList, isShowUnpatroled } = await this.analyticsService.onlinePathConfig(query,DateReport.Subject.OnlinePatrol);
            if (noManager) return { addressSelects: [], analyticPersons: [] }
            /** 获取所需数据 */
            const persons: Analytics.AnalyticsPersonRes = await this.projectPatrolDao.analyticsPerson(query);
            const managerIds = (persons?.analyticPersons || [])?.map(m => m.managerId);
            const deptList = _.isEmpty(managerIds)?[]:await this.departmentMemberDao.searchDeptInfoList({managerIds})
            const managerDeptMap = _.keyBy(deptList, 'managerId')
            /** 为统计数据加入员工名称 可以直接通过 dealManagerList 获取manager信息 不需要重新从数据库中查找 */
            for (const person of persons.analyticPersons) {
                const manager = dealManagerList?.find(mg => mg.managerId == person.managerId)
                if (manager) person.nickName = manager.nickName;
                if (managerDeptMap[person.managerId]) {
                    person.personDepartmentId = managerDeptMap[person.managerId].deptId;
                    person.personDepartmentName = managerDeptMap[person.managerId].deptName;
                }
            }
            if (!isAllRole && dealManagerList instanceof Array) {
                const {managerZeros} = this.analyticsService.getRealManagerInfo({ isAllRole, dealManagerList, persons: persons.analyticPersons })
                const managerIdList = managerZeros.map(m => m.managerId)
                const managers = managerIdList.length == 0 ? [] : await this.managerDao.getBelongToListByDeptId({ managerIdList });
                const managerMap = _.keyBy(managers, 'managerId');
                persons.analyticPersons.push(...managerZeros.map(p => ({
                    ...p, 
                    personDepartmentId: managerMap[p.managerId]?.departmentId,
                    personDepartmentName: managerMap[p.managerId]?.departmentName,
                 } as any)))
            }
            // 筛选了岗位且公司设置展示未巡检数据
            if (!isAllRole && !!dealManagerList.length && isShowUnpatroled === 'Y') {

                const managerIds = (persons?.analyticPersons || []).map(m => m.managerId);
                if (!_.isEmpty(managerIds) && body.companyId) {
                    const managerPatroledList = await this.onsitePatrolDao.searchProjectShouldPatroledV2({ companyIds: query.companyIds, managerIds, startDate: query.from, endDate: query.to, roleNames: query.roleNames });
                    const projectIdList = []
                    managerPatroledList?.map(item => {
                        if(!_.isEmpty(item.projectIds)) projectIdList.push(...item.projectIds)
                    });
                    let bindProjectList = []
                    if (!_.isEmpty(projectIdList)) {
                        const bindProject = await this.dimDeviceDao.search({ companyIdList: query.companyIds, projectIdList, startTime: query.from, endTime: query.to, isBindProject: 'Y' });
                        bindProjectList = bindProject?.items || []
                    }
                    const bindProjectIds = bindProjectList?.map(item => item.projectId)
                    const managerPatroledMap: { [key: string]: { managerId: string, projectIds: string[] } } = managerPatroledList.reduce((prev, cur) => { prev[cur.managerId] = cur; return prev; }, {});
                    for (const person of persons.analyticPersons) {
                        let managerPatroledProjectIds = managerPatroledMap[person.managerId]?.projectIds || [];
                        managerPatroledProjectIds = managerPatroledProjectIds.filter(item => bindProjectIds.includes(item));
                        person.unpatroledCount = _.difference(managerPatroledProjectIds, (person?.projectIds?.split(",") || [])).length;
                    }
                }
            }
            return persons;
        } catch (e) {
            this.logger.log(`projectPatrolService analyticsPerson 错误: ${String(e)}`);
            throw new YqzException(`projectPatrolService analyticsPerson 错误: ${String(e)}`)
        }
    }

    /** 线上巡检分析 岗位分布  */
    @CheckFormatCsv(Analytics.csvPosition, "analyticsPositions")
    async analyticsPosition(body: Analytics.Req): Promise<Analytics.analyticsPositionRes> {
        try {
            const result: Analytics.analyticsPositionRes = {
                dealPosition: '0',
                totalPosition: 0,
                dealManager: '0',
                totalManager: 0,
                totalProject: 0,
                totalPatrol: 0,
                analyticsPositions: []
            }
            //统一处理数据报表请求参数
            const query = await this.analyticsService.onlineBodyParse({...body, module: DateReport.Subject.OnlinePatrol});
            if (!query)
                return result;
            /** 再次过滤 公司配置 */
            const { noManager, isAllRole, dealPostList, dealManagerList, isCustomerCase1 } = await this.analyticsService.onlinePathConfig(query,DateReport.Subject.OnlinePatrol);
            if (noManager) return result;
            const postRes: Analytics.analyticsPositionRes = await this.projectPatrolDao.analyticsPosition(query);

            /** 为了 dealManager 实际巡检的人数 */
            if (!isAllRole && dealManagerList instanceof Array) {
                const persons: Analytics.AnalyticsPersonRes = await this.projectPatrolDao.analyticsPerson(query);
                const { realManagerList, realManager,managerZeros } = this.analyticsService.getRealManagerInfo({ isAllRole, dealManagerList, persons: persons.analyticPersons })
                persons.analyticPersons.push(...managerZeros)

                const { realPost, postZeros } = this.analyticsService.getPositionInfo({ isAllRole, dealPostList, realManagerList, postRes: postRes.analyticsPositions, isCustomerCase1 })
                postRes.analyticsPositions.push(...postZeros);

                postRes.dealManager = String(realManager);
                postRes.dealPosition = String(realPost);
            }

            // 合并相同的岗位
            postRes.analyticsPositions = ObjUtil.mergeByFields(postRes.analyticsPositions, "roleName")

            return postRes;
        } catch (e) {
            this.logger.log(`projectPatrolService analyticsPosition 错误: ${String(e)}`);
            throw new YqzException(`projectPatrolService analyticsPosition 错误: ${String(e)}`)
        }
    }

    /** 线上巡检分析 数据明细 */
    @CheckFormatCsv((body) => {
        if (body?.companyType === Company.CompanyType.Conglomerate) return Analytics.csvList
        return _.filter(Analytics.csvList, item => item.key !== "linkCompanyName");//不是集团公司不需要展示所属公司
    })
    async analyticsList(body: Analytics.Req) {
        try {
            this.logger.log("analytics/list service: ", JSON.stringify(body))
            const { companyId } = body;
            const result = { total: 0, items: [] }
            //统一处理数据报表请求参数
            const query = await this.analyticsService.onlineBodyParse({...body, module: DateReport.Subject.OnlinePatrol});
            if (!query) return result // 如果 格式化不成功 或者提前知道不会查找到数据 直接返回
            /** 再次过滤 公司配置 */
            const { noManager,isAllRole, isShowUnpatroled } = await this.analyticsService.onlinePathConfig(query,DateReport.Subject.OnlinePatrol);
            if (noManager) return result
            const companyIds = query?.linkCompanyId ? [query?.linkCompanyId]:query.companyIds
            //筛选-工地负责人
            let searchProjectIds = []
            if (query.projectAddress && !_.isEmpty(companyIds)) {
                searchProjectIds = await this.projectDao.getProjectIds({
                    projectDirectorId: query.projectDirectorId,//绑定工地(PC)
                    companyIds: companyIds,
                    projectAddress: query.projectAddress
                });
                searchProjectIds = searchProjectIds.map(res => res.projectId);
                if (_.isEmpty(searchProjectIds)) return result
                query.projectIds = searchProjectIds;
            }
            // 查询应巡检工地
            // const res = await this.onsitePatrolDao.searchProjectShouldPatroledV2({ companyIds: companyIds, projectIds: searchProjectIds, managerIds: query.managerIdList, startDate: query.from, endDate: query.to, roleNames: query.roleNames });
            // const shouldPatrols = []
            // res.forEach(item => {
            //     item.projectIds.forEach(projectId => {
            //         shouldPatrols.push({ managerId: item.managerId, projectId, roleName: item.roleName })
            //     })
            // })
            // searchProjectIds = shouldPatrols
            // searchProjectIds = searchProjectIds.map(res => res.projectId);
            

            if (!isAllRole && isShowUnpatroled === 'Y') {
                const pageNo = query.pageNo
                const pageSize = query.pageSize
                delete query.pageNo
                delete query.pageSize
                const { items: list } = await this.projectPatrolDao.analyticsList(query);
                const patroledList = list as any[]

                let shouldPatroledList = []
                const res = await this.onsitePatrolDao.searchProjectShouldPatroledV2({ companyIds: query.companyIds, projectIds: searchProjectIds, managerIds: query.managerIdList, startDate: query.from, endDate: query.to, roleNames: query.roleNames });
                const projectIdList = []
                res.map(item => {
                    if(!_.isEmpty(item.projectIds)) 
                        projectIdList.push(...item.projectIds)
                })
                let bindProjectList = []
                if (!_.isEmpty(projectIdList)) {
                    const bindProject = await this.dimDeviceDao.search({ companyIdList: query.companyIds, projectIdList: projectIdList, startTime: query.from, endTime: query.to, isBindProject: 'Y' });
                    bindProjectList = bindProject?.items || []
                }
                const bindProjectIds = bindProjectList?.map(item => item.projectId)
                res.forEach(item => {
                    item.projectIds.forEach(projectId => {
                        if (bindProjectIds.includes(projectId))
                            shouldPatroledList.push({ managerId: item.managerId, projectId, roleName: item.roleName })
                    })
                })
                shouldPatroledList.sort((a, b) => {
                    if (a.managerId !== b.managerId) {
                        return Number(b.managerId) - Number(a.managerId)
                    } else {
                        return Number(b.projectId) - Number(a.projectId)
                    }
                })

                const unpatroledList = _.differenceWith(shouldPatroledList, patroledList, (a, b) => a.managerId === b.managerId && a.projectId === b.projectId)

                let mergedList = []
                if (query.isPatroled === 'N') {
                    mergedList = unpatroledList
                }else if (query.isPatroled === 'Y') {
                    mergedList = patroledList
                } else {
                    mergedList = [...patroledList, ...unpatroledList]
                }

                const total = mergedList.length;
                let items;
                if (pageNo && pageSize) {
                    items = mergedList.slice((pageNo - 1) * pageSize, pageNo * pageSize)
                } else {
                    items = mergedList;
                }
                
                //查询员工部门信息
                const managerMap = await this.managerService.searchManagerInfoMap({ managerIdList: items.map(res => res.managerId) });
                //查询工地地址
                const projectIds = items.map(res => res.projectId)
                const extraInfoMap = await this.projectService.getProjectAddressByProjectIds({ projectIds: projectIds });
                const isCustomCompany = await this.companyCustomConfigService.isCustomCompany({ companyId, customType: 'project_address' });
                //工地信息
                const projects = projectIds.length == 0 ? [] : await this.projectDao.getOnsiteProjectInfos({ projectIdList: projectIds });
                const projectMap = _.keyBy(projects, 'projectId');
                /** 格式化数据 */
                items.forEach(item => {
                    item.address = item.projectName?((isCustomCompany ? item.projectName : item.communityName) + item.houseNumber):extraInfoMap[item.projectId];
                    item.patrolTime = item.patrolTime || "未巡检"
                    item.projectStartTime = projectMap[item.projectId]?.projectStartTime?format(projectMap[item.projectId]?.projectStartTime,"yyyy-MM-dd"):"";
                    item.projectCompletedTime = projectMap[item.projectId]?.projectCompletedTime?format(projectMap[item.projectId]?.projectCompletedTime,"yyyy-MM-dd"):"";
                    item.nickName = managerMap[item.managerId]?.nickName;
                    item.departmentId = managerMap[item.managerId]?.departmentId;
                    item.departmentName = managerMap[item.managerId]?.departmentName;
                    delete item.projectName;
                    delete item.communityName;
                    delete item.houseNumber;
                })
                return { total, items };
            }
            /** 查找数据 */
            const { total, items } = await this.projectPatrolDao.analyticsList(query);
            const isCustomCompany = await this.companyCustomConfigService.isCustomCompany({ companyId, customType: 'project_address' });
            /** 格式化数据 */
            items.forEach(item => {
                item.address = (isCustomCompany ? item.projectName : item.communityName) + item.houseNumber;
                delete item.projectName;
                delete item.communityName;
                delete item.houseNumber;
            })
            return { total, items };
        } catch (e) {
            this.logger.log(`projectPatrolService analyticsList 错误: ${String(e)}`);
            throw new YqzException(`projectPatrolService analyticsList 错误: ${String(e)}`)
        }
    }
}