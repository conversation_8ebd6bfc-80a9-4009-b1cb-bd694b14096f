import { Injectable } from "@nestjs/common";
import { MyLogger } from "@src/modules/logger/my-logger.service";
import { DataSource } from "typeorm";
import * as _ from "lodash";
import { Common, DevTimeout } from "@yqz/nest";
import { ProjectUpdateEntity } from "@src/modules/report/entity/bgw/project-update.entity";
import { ManagerEntity } from "@src/modules/report/entity/bgw/manager.entity";
import { CompanyMemberRoleEntity } from "@src/modules/report/entity/bgw/company-member-role.entity";
import { ProjectMemberEntity } from "@src/modules/report/entity/bgw/project-member.entity";
import { MemberRoleEntity } from "@src/modules/report/entity/bgw/member-role.entity";
import { ProjectEntity } from "@src/modules/report/entity/bgw/project.entity";
import { StageEntity } from "@src/modules/report/entity/bgw/stage.entity";
import { ProjectSignUpdate } from "../dto/project-sign-update.dto";
import { ProjectUpdateMediaEntity } from "@src/modules/report/entity/bgw/project-update-media.entity";
import { CmsPhotoEntity } from "@src/modules/report/entity/bgw/cms-photo.entity";

@Injectable()
export class ProjectSignUpdateDao {
    private readonly logger = new MyLogger(ProjectSignUpdateDao.name)
    constructor(
        private dataSource: DataSource
    ) { }

    @DevTimeout(500)
    async test() {
    }

    /**
     * 工地看板更新统计
     * @param request 
     * @returns 
     */
    async projectUpdateStatistics(request: { companyId: string, projectId?: string, startTime?: string, endTime?: string }) {
        const { companyId, projectId, startTime, endTime } = request;
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'COUNT(DISTINCT m.person_id) as totalPerson',
                'COUNT(DISTINCT pu.project_update_id) as totalUpdate'
            ])
            .from(ManagerEntity, 'm')
            .innerJoin(ProjectUpdateEntity, 'pu', 'm.PERSON_ID = pu.person_id')
            .where('pu.delete_flag = :delFlag', { delFlag: Common.Flag.N })
            .andWhere('m.company_id = :companyId', { companyId })
            .andWhere('pu.company_id = :companyId', { companyId })
            .andWhere('pu.project_update_type = "12"');
        if (projectId) qb.andWhere('pu.project_id = :projectId', { projectId });
        if (startTime) qb.andWhere('pu.createTime >= :startTime', { startTime });
        if (endTime) qb.andWhere('pu.createTime <= :endTime', { endTime });
        qb.groupBy('pu.project_id');
        return await qb.getRawOne<ProjectSignUpdate.StatisticsRes>();
    }

    /**
     * 工地更新根据manager分组统计（包含工地更新的manager和工地成员）
     * 注：工地更新不一定是工地成员，工地成员即使没更新也要展示
     * @param request 
     * @returns 
     */
    async projectUpdateManagerGroupCount(request: { companyId: string, projectId: string, startTime?: string, endTime?: string }): Promise<number> {
        const { companyId, projectId, startTime, endTime } = request;
        const tb1 = this.dataSource.createQueryBuilder()
            .select(['m.person_id personId'])
            .from(ManagerEntity, 'm')
            .innerJoin(ProjectMemberEntity, 'pm', 'm.PERSON_ID = pm.person_id')
            .leftJoin(MemberRoleEntity, 'r', 'pm.PROJECT_MEMBER_ID = r.PROJECT_MEMBER_ID')
            .where('pm.delete_flag = :delFlag', { delFlag: Common.Flag.N })
            .andWhere('m.delete_flag = :delFlag', { delFlag: Common.Flag.N })
            .andWhere('m.company_id = :companyId', { companyId })
            .andWhere('pm.project_id = :projectId', { projectId })
            .andWhere('r.role_id in (:...roles)', { roles: ['1', '2'] })
            .groupBy('m.person_id');

        const tb2 = this.dataSource.createQueryBuilder()
            .select(['m.person_id personId'])
            .from(ManagerEntity, 'm')
            .innerJoin(ProjectUpdateEntity, 'pu', 'm.PERSON_ID = pu.person_id')
            .where('pu.delete_flag = :delFlag', { delFlag: Common.Flag.N })
            .andWhere('m.company_id = :companyId', { companyId })
            .andWhere('pu.company_id = :companyId', { companyId })
            .andWhere('pu.project_id = :projectId', { projectId })
            .andWhere('pu.project_update_type = "12"');
        if (startTime) tb2.andWhere('pu.createTime >= :startTime', { startTime });
        if (endTime) tb2.andWhere('pu.createTime <= :endTime', { endTime });
        tb2.groupBy('m.person_id');

        const qb = this.dataSource.createQueryBuilder()
            .select(['count(DISTINCT qb.personId) num'])
            .from("((" + tb1.getQuery() + ") union all (" + tb2.getQuery() + "))", "qb")
        qb.setParameters(tb1.getParameters());
        qb.setParameters(tb2.getParameters());
        const res = await qb.getRawOne();
        return Number(res?.num || 0);
    }

    /**
     * 工地更新根据manager分组（包含工地更新的manager和工地成员）
     * 注：工地更新不一定是工地成员，工地成员即使没更新也要展示
     * @param request 
     * @returns 
     */
    async projectUpdateManagerGroup(request: { companyId: string, projectId: string, startTime?: string, endTime?: string, pageNo?: number, pageSize?: number }) {
        const { companyId, projectId, startTime, endTime, pageNo, pageSize } = request;
        const tb1 = this.dataSource.createQueryBuilder()
            .select([
                'm.person_id personId',
                'm.manager_id managerId',
                'm.nick_name nickName',
                'm.profile_photo profilePhoto',
                'm.role_id roleId',
                'mr.role_name roleName',
                '0 as totalUpdate',
                'pm.createTime as joinProjectTime'
            ])
            .from(ManagerEntity, 'm')
            .innerJoin(ProjectMemberEntity, 'pm', 'm.PERSON_ID = pm.person_id')
            .leftJoin(MemberRoleEntity, 'r', 'pm.PROJECT_MEMBER_ID = r.PROJECT_MEMBER_ID')
            .leftJoin(CompanyMemberRoleEntity, 'mr', 'mr.COMPANY_MEMBER_ROLE_ID = m.ROLE_ID')
            .where('pm.delete_flag = :delFlag', { delFlag: Common.Flag.N })
            .andWhere('m.delete_flag = :delFlag', { delFlag: Common.Flag.N })
            .andWhere('m.company_id = :companyId', { companyId })
            .andWhere('pm.project_id = :projectId', { projectId })
            .andWhere('r.role_id in (:...roles)', { roles: ['1', '2'] })
            .groupBy('m.person_id');

        const tb2 = this.dataSource.createQueryBuilder()
            .select([
                'm.person_id personId',
                'm.manager_id managerId',
                'm.nick_name nickName',
                'm.profile_photo profilePhoto',
                'm.role_id roleId',
                'mr.role_name roleName',
                'COUNT(DISTINCT pu.project_update_id) as totalUpdate',
                'null as joinProjectTime'
            ])
            .from(ManagerEntity, 'm')
            .innerJoin(ProjectUpdateEntity, 'pu', 'm.PERSON_ID = pu.person_id')
            .innerJoin("(SELECT PERSON_ID, MAX(createTime) createTime FROM manager WHERE COMPANY_ID = :companyId GROUP BY PERSON_ID)", 'delm', "delm.PERSON_ID = m.PERSON_ID AND delm.createTime = m.createTime", { companyId })
            .leftJoin(CompanyMemberRoleEntity, 'mr', 'mr.COMPANY_MEMBER_ROLE_ID = m.ROLE_ID')
            .where('pu.delete_flag = :delFlag', { delFlag: Common.Flag.N })
            .andWhere('m.company_id = :companyId', { companyId })
            .andWhere('pu.company_id = :companyId', { companyId })
            .andWhere('pu.project_id = :projectId', { projectId })
            .andWhere('pu.project_update_type = "12"');
        if (startTime) tb2.andWhere('pu.createTime >= :startTime', { startTime });
        if (endTime) tb2.andWhere('pu.createTime <= :endTime', { endTime });
        tb2.groupBy('m.person_id');

        const qb = this.dataSource.createQueryBuilder()
            .select([
                'qb.managerId',
                'qb.nickName',
                'qb.profilePhoto',
                'qb.roleId',
                'qb.roleName',
                'SUM(qb.totalUpdate) totalUpdate'
            ])
            .from("((" + tb1.getQuery() + ") union all (" + tb2.getQuery() + "))", "qb")
            .groupBy('qb.personId')
            .orderBy('totalUpdate', 'DESC')
            .addOrderBy('qb.joinProjectTime is null', 'ASC')
            .addOrderBy('qb.joinProjectTime', 'ASC')
            .addOrderBy('qb.managerId', 'ASC')
        if (pageSize && pageNo) qb.limit(pageSize).offset((pageNo - 1) * pageSize);
        qb.setParameters(tb1.getParameters());
        qb.setParameters(tb2.getParameters());
        return await qb.getRawMany<ProjectSignUpdate.ManagerGroupRes>();
    }


    /**
     * 工地更新根据阶段分组（如果工地更新的阶段不等于工地当前阶段，统计为其他）
     * @param request 
     * @returns 
     */
    async projectUpdateStageGroup(request: { companyId: string, projectId: string, startTime?: string, endTime?: string }) {
        const { companyId, projectId, startTime, endTime } = request;
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'case when p.STAGE_TEMPLATE_ID = pu.STAGE_TEMPLATE_ID then s.STAGE_ID else null end as stageId',
                'case when p.STAGE_TEMPLATE_ID = pu.STAGE_TEMPLATE_ID then s.STAGE_NAME else "其他" end as stageName',
                'case when p.STAGE_TEMPLATE_ID = pu.STAGE_TEMPLATE_ID then s.sort else null end as stageSort',
                'COUNT(DISTINCT pu.project_update_id) as totalUpdate',
            ])
            .from(ManagerEntity, 'm')
            .innerJoin(ProjectUpdateEntity, 'pu', 'm.PERSON_ID = pu.person_id')
            .leftJoin(ProjectEntity, 'p', 'pu.project_id = p.project_id')
            .leftJoin(StageEntity, 's', 's.STAGE_TEMPLATE_ID = pu.STAGE_TEMPLATE_ID and s.BUSINESS_STAGE_ID = pu.BUSINESS_STAGE_ID')
            .where('pu.delete_flag = :delFlag', { delFlag: Common.Flag.N })
            .andWhere('s.delete_flag = :delFlag', { delFlag: Common.Flag.N })
            .andWhere('m.company_id = :companyId', { companyId })
            .andWhere('pu.company_id = :companyId', { companyId })
            .andWhere('pu.project_id = :projectId', { projectId })
            .andWhere('pu.project_update_type = "12"');
        if (startTime) qb.andWhere('pu.createTime >= :startTime', { startTime });
        if (endTime) qb.andWhere('pu.createTime <= :endTime', { endTime });
        qb.groupBy('stageId');
        qb.orderBy('stageSort is null', 'ASC').addOrderBy('stageSort', 'ASC');
        return await qb.getRawMany<ProjectSignUpdate.StageGroupRes>();
    }

    /**
     * 更新列表
     * @param request 
     * @returns 
     */
    async projectUpdateList(request: { companyId: string, projectId: string, stageTemplateId?: string, businessStageId?: string, managerId?: string, startTime?: string, endTime?: string, pageNo?: number, pageSize?: number }) {
        const { companyId, projectId, stageTemplateId, businessStageId, managerId, startTime, endTime, pageNo, pageSize } = request;
        const qb = this.dataSource.createQueryBuilder()
            .select([
                's.stage_name as stageName',
                's.STAGE_TEMPLATE_ID as stageTemplateId',
                's.BUSINESS_STAGE_ID as businessStageId',
                'm.manager_id as managerId',
                'm.nick_name as nickName',
                'mr.role_name as roleName',
                'm.role_id as roleId',
                'DATE_FORMAT(pu.project_update_time, "%Y-%m-%d %H:%i:%s") as projectUpdateTime',
                'pu.project_update_text as projectUpdateText',
                'pu.project_update_id as projectUpdateId'
                // 'pu.project_update_media_list as projectUpdateMediaList'
            ])
            .from(ManagerEntity, 'm')
            .innerJoin(ProjectUpdateEntity, 'pu', 'm.PERSON_ID = pu.person_id')
            .innerJoin("(SELECT PERSON_ID, MAX(createTime) createTime FROM manager WHERE COMPANY_ID = :companyId GROUP BY PERSON_ID)", 'delm', "delm.PERSON_ID = m.PERSON_ID AND delm.createTime = m.createTime", { companyId })
            .leftJoin(StageEntity, 's', 's.STAGE_TEMPLATE_ID = pu.STAGE_TEMPLATE_ID and s.BUSINESS_STAGE_ID = pu.BUSINESS_STAGE_ID')
            .leftJoin(CompanyMemberRoleEntity, 'mr', 'mr.COMPANY_MEMBER_ROLE_ID = m.ROLE_ID')
            .where('pu.delete_flag = :delFlag', { delFlag: Common.Flag.N })
            .andWhere('s.delete_flag = :delFlag', { delFlag: Common.Flag.N })
            .andWhere('m.company_id = :companyId', { companyId })
            .andWhere('pu.company_id = :companyId', { companyId })
            .andWhere('pu.project_id = :projectId', { projectId })
            .andWhere('pu.project_update_type = "12"');
        if (managerId) qb.andWhere('m.manager_id = :managerId', { managerId });
        if (stageTemplateId) qb.andWhere('pu.STAGE_TEMPLATE_ID = :stageTemplateId', { stageTemplateId });
        if (businessStageId) qb.andWhere('pu.BUSINESS_STAGE_ID = :businessStageId', { businessStageId });
        if (startTime) qb.andWhere('pu.createTime >= :startTime', { startTime });
        if (endTime) qb.andWhere('pu.createTime <= :endTime', { endTime });
        qb.orderBy('pu.createTime', 'DESC').addOrderBy('pu.project_update_id', 'DESC');
        if (pageSize && pageNo) qb.limit(pageSize).offset((pageNo - 1) * pageSize);
        return await qb.getRawMany<ProjectSignUpdate.ListRes>();
    }

    /**
     * 查询更新图片
     * @param request 
     * @returns 
     */
    async searchProjectUpdateMediaByUpdateIds(updateIds: string[]) {
        const qb = this.dataSource.getRepository(ProjectUpdateMediaEntity)
            .createQueryBuilder('pum')
            .select([
                'pum.project_update_id as id', 
                'pum.photo_id as photoId',
                'cms.media_type as mediaType',
                'cms.oss_url as ossUrl',
                'cms.media_resource_url as mediaResourceUrl',
                'cms.created_date as createdDate',
                'cms.address as address'
            ])
            .leftJoin(CmsPhotoEntity,'cms', 'cms.photo_id = pum.photo_id')
            .where('pum.project_update_id IN (:...updateIds)', { updateIds })
            .andWhere('pum.delete_flag = :delFlag', { delFlag: Common.Flag.N })
        return await qb.getRawMany();
    }

    /**
     * 更新列表统计
     * @param request 
     * @returns 
     */
    async projectUpdateCount(request: { companyId: string, projectId: string, stageTemplateId?: string, businessStageId?: string, managerId?: string, startTime?: string, endTime?: string }) {
        const { companyId, projectId, stageTemplateId, businessStageId, managerId, startTime, endTime } = request;
        const qb = this.dataSource.createQueryBuilder()
            .select(['count(DISTINCT pu.project_update_id) num'])
            .from(ManagerEntity, 'm')
            .innerJoin(ProjectUpdateEntity, 'pu', 'm.PERSON_ID = pu.person_id')
            .leftJoin(StageEntity, 's', 's.STAGE_TEMPLATE_ID = pu.STAGE_TEMPLATE_ID and s.BUSINESS_STAGE_ID = pu.BUSINESS_STAGE_ID')
            .where('pu.delete_flag = :delFlag', { delFlag: Common.Flag.N })
            .andWhere('s.delete_flag = :delFlag', { delFlag: Common.Flag.N })
            .andWhere('m.company_id = :companyId', { companyId })
            .andWhere('pu.company_id = :companyId', { companyId })
            .andWhere('pu.project_id = :projectId', { projectId })
            .andWhere('pu.project_update_type = "12"');
        if (managerId) qb.andWhere('m.manager_id = :managerId', { managerId });
        if (stageTemplateId) qb.andWhere('pu.STAGE_TEMPLATE_ID = :stageTemplateId', { stageTemplateId });
        if (businessStageId) qb.andWhere('pu.BUSINESS_STAGE_ID = :businessStageId', { businessStageId });
        if (startTime) qb.andWhere('pu.createTime >= :startTime', { startTime });
        if (endTime) qb.andWhere('pu.createTime <= :endTime', { endTime });
        const res = await qb.getRawOne();
        return Number(res?.num || 0);
    }

    /**
     * 工地更新阶段列表
     * @param request 
     * @returns 
     */
    async projectUpdateStageList(request: { companyId: string, projectId: string }) {
        const { companyId, projectId } = request;
        const qb = this.dataSource.createQueryBuilder()
            .select([
                's.stage_name as stageName',
                's.STAGE_TEMPLATE_ID as stageTemplateId',
                's.BUSINESS_STAGE_ID as businessStageId'
            ])
            .from(ManagerEntity, 'm')
            .innerJoin(ProjectUpdateEntity, 'pu', 'm.PERSON_ID = pu.person_id')
            .leftJoin(StageEntity, 's', 's.STAGE_TEMPLATE_ID = pu.STAGE_TEMPLATE_ID and s.BUSINESS_STAGE_ID = pu.BUSINESS_STAGE_ID')
            .leftJoin(ProjectEntity, 'p', 'pu.project_id = p.project_id')
            .where('pu.delete_flag = :delFlag', { delFlag: Common.Flag.N })
            .andWhere('s.delete_flag = :delFlag', { delFlag: Common.Flag.N })
            .andWhere('m.company_id = :companyId', { companyId })
            .andWhere('pu.company_id = :companyId', { companyId })
            .andWhere('pu.project_id = :projectId', { projectId })
            .andWhere('pu.project_update_type = "12"')
            .groupBy('s.stage_id')
            .orderBy('s.STAGE_TEMPLATE_ID = p.STAGE_TEMPLATE_ID', 'DESC').addOrderBy('s.sort', 'ASC');//当前阶段模板排在前面
        return await qb.getRawMany<ProjectSignUpdate.StageOptions>();
    }

    /**
     * 工地更新更新人列表
     * @param request 
     * @returns 
     */
    async projectUpdateManagerList(request: { companyId: string, projectId: string }) {
        const { companyId, projectId } = request;
        const qb = this.dataSource.createQueryBuilder()
            .select([
                'm.manager_id as managerId',
                'm.nick_name as nickName'
            ])
            .from(ManagerEntity, 'm')
            .innerJoin(ProjectUpdateEntity, 'pu', 'm.PERSON_ID = pu.person_id')
            .innerJoin("(SELECT PERSON_ID, MAX(createTime) createTime FROM manager WHERE COMPANY_ID = :companyId GROUP BY PERSON_ID)", 'delm', "delm.PERSON_ID = m.PERSON_ID AND delm.createTime = m.createTime", { companyId })
            .leftJoin(StageEntity, 's', 's.STAGE_TEMPLATE_ID = pu.STAGE_TEMPLATE_ID and s.BUSINESS_STAGE_ID = pu.BUSINESS_STAGE_ID')
            .where('pu.delete_flag = :delFlag', { delFlag: Common.Flag.N })
            .andWhere('s.delete_flag = :delFlag', { delFlag: Common.Flag.N })
            .andWhere('m.company_id = :companyId', { companyId })
            .andWhere('pu.company_id = :companyId', { companyId })
            .andWhere('pu.project_id = :projectId', { projectId })
            .andWhere('pu.project_update_type = "12"')
            .groupBy('m.person_id')
            .orderBy('m.manager_id', 'ASC');
        return await qb.getRawMany<ProjectSignUpdate.ManagerOptions>();
    }

}