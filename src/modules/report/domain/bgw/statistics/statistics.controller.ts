import { Body, Controller, Post } from "@nestjs/common";
import { DataAvailabilityService } from "./service/data-availability.service";
import { JwtUserGw, MyUser } from "@yqz/nest";
import { StatisticsService } from "./service/statistics.service";

@Controller("statistics")
export class StatisticsController {
    constructor(
        private readonly dataAvailabilityService: DataAvailabilityService,
        private readonly statisticsService: StatisticsService,
    ) { }

    @Post("availability-date/list")
    async searchDataAvailability(@Body() body: {type: string, params: any},@MyUser() user: JwtUserGw): Promise<{ date: string[] }> {
        body.params.companyId = user?.targetCompany?.companyId;
        body.params.personId = user?.personId;
        return await this.dataAvailabilityService.getDataAvailability(body.type, body.params);
    }

    @Post("customer-aggregate/list")
    async searchCustomerAggregateData(@Body() body: {format?: string}) {
        const res = await this.statisticsService.searchCustomerAggregateData(body);
        return res
    }
}