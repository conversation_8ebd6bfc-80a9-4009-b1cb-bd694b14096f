import { Injectable } from "@nestjs/common";
import { StatisticsDao } from "../dao/statistics.dao";
import { CheckFormatCsv } from "@yqz/nest";
import { Statistics } from "../types/statistics.type";

@Injectable()
export class StatisticsService {
    constructor(
        private readonly statisticsDao: StatisticsDao
    ) { }

    @CheckFormatCsv(Statistics.CustomerAggregateDataCsvList)
    async searchCustomerAggregateData(params: {format?: string}) {
        const items = await this.statisticsDao.searchCustomerAggregateData();
        return {
            items
        }
    }
}