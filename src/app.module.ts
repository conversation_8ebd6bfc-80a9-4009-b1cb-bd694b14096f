import { TypeOrmModule } from "@nestjs/typeorm";
import { Modu<PERSON> } from "@nestjs/common";
import { AppController } from "./app.controller";
import { AppService } from "./app.service";
import { LoggerModule } from "./modules/logger/my-logger.module";
import { ScheduleModule } from "@nestjs/schedule";
import { ReportModule } from "./modules/report/report.module";
import { ExternalMerchantModule } from "./modules/external-merchant/external-merchant.module";
import { MyMongoDb } from "./modules/report/mongodb/mongodb.connection";

@Module({
  imports: [
    ScheduleModule.forRoot(),
    TypeOrmModule.forRoot({
      type: process.env.TYPEORM_CONNECTION as any,
      database: process.env.TYPEORM_DATABASE,
      charset: process.env.TYPEORM_CHARSET,
      entities: [process.env.TYPEORM_ENTITIES],
      host: process.env.TYPEORM_HOST,
      logging: process.env.TYPEORM_LOGGING as any,
      password: process.env.TYPEORM_PASSWORD,
      port: Number(process.env.TYPEORM_PORT),
      username: process.env.TYPEORM_USERNAME,
      pool: {
        max: Number(process.env.TYPEORM_CONNECTION_LIMIT)
      }
    }),
    LoggerModule,
    ReportModule,
    ExternalMerchantModule
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {
  constructor() {
    //使用的是原生 MongoDB 驱动
    MyMongoDb.clientConnect();
  }
}
